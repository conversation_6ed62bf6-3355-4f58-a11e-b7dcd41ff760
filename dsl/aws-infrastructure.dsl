workspace "AWS Infrastructure - Kidjo" {

    !identifiers hierarchical
    !docs docs
    !adrs adrs

    model {
        
        # External actors
        developer = person "Developer" "Software developers pushing code changes" "person"
        endUser = person "End User" "Application users accessing the system" "person"
        contentManager = person "Content Manager" "Users uploading large content files" "person"
        
        # External systems
        externalMonitoring = softwareSystem "External Monitoring" "Third-party monitoring and alerting systems" "external"
        
        # Main AWS Infrastructure System
        awsInfrastructure = softwareSystem "AWS Infrastructure" "Complete AWS cloud infrastructure for Kidjo platform" {
            
            # Storage Services
            s3 = container "Amazon S3" {
                description "Object storage for static content, backups, and file uploads"
                technology "Amazon S3"
                tag "Amazon Web Services - Simple Storage Service"
            }
            
            # Content Delivery
            cloudFront = container "Amazon CloudFront" {
                description "CDN for website hosting and content delivery"
                technology "Amazon CloudFront"
                tag "Amazon Web Services - CloudFront"
            }
            
            # Compute Services
            beanstalk = container "AWS Elastic Beanstalk" {
                description "Platform for deploying and managing backend applications"
                technology "AWS Elastic Beanstalk"
                tag "Amazon Web Services - Elastic Beanstalk"
            }
            
            ec2 = container "Amazon EC2" {
                description "Virtual servers hosting backend services"
                technology "Amazon EC2"
                tag "Amazon Web Services - EC2"
            }
            
            # Database Services
            aurora = container "Amazon Aurora RDS" {
                description "Managed relational database for application data"
                technology "Amazon Aurora"
                tag "Amazon Web Services - Aurora"
            }
            
            elasticache = container "Amazon ElastiCache" {
                description "In-memory caching service for performance optimization"
                technology "Amazon ElastiCache"
                tag "Amazon Web Services - ElastiCache"
            }
            
            # Networking & Load Balancing
            alb = container "Application Load Balancer" {
                description "Load balancer distributing traffic to backend services"
                technology "AWS ALB"
                tag "Amazon Web Services - Elastic Load Balancing Application Load Balancer"
            }
            
            # Security & Certificates
            acm = container "AWS Certificate Manager" {
                description "SSL/TLS certificate management"
                technology "AWS ACM"
                tag "Amazon Web Services - Certificate Manager"
            }
            
            iam = container "AWS IAM" {
                description "Identity and access management for users and permissions"
                technology "AWS IAM"
                tag "Amazon Web Services - Identity and Access Management IAM"
            }
            
            # File Transfer
            transferFamily = container "AWS Transfer Family" {
                description "SFTP service for secure file uploads to S3"
                technology "AWS Transfer Family"
                tag "Amazon Web Services - AWS Transfer Family"
            }
            
            # Analytics Pipeline
            apiGateway = container "Amazon API Gateway" {
                description "API management for analytics endpoints"
                technology "Amazon API Gateway"
                tag "Amazon Web Services - API Gateway"
            }
            
            lambda = container "AWS Lambda" {
                description "Serverless functions for analytics processing"
                technology "AWS Lambda"
                tag "Amazon Web Services - Lambda"
            }
            
            sqs = container "Amazon SQS" {
                description "Message queue for analytics event processing"
                technology "Amazon SQS"
                tag "Amazon Web Services - Simple Queue Service"
            }
            
            # Communication Services
            ses = container "Amazon SES" {
                description "Email service for sending notifications and alerts"
                technology "Amazon SES"
                tag "Amazon Web Services - Simple Email Service"
            }
            
            sns = container "Amazon SNS" {
                description "Notification service for deployment and health alerts"
                technology "Amazon SNS"
                tag "Amazon Web Services - Simple Notification Service"
            }
            
            # Scheduling
            eventBridge = container "Amazon EventBridge" {
                description "Event-driven scheduling for automated jobs"
                technology "Amazon EventBridge"
                tag "Amazon Web Services - EventBridge"
            }
            
            # Monitoring & Logging
            cloudWatch = container "Amazon CloudWatch" {
                description "Monitoring, logging, and alerting service"
                technology "Amazon CloudWatch"
                tag "Amazon Web Services - CloudWatch"
            }
            
            cloudTrail = container "AWS CloudTrail" {
                description "API logging and audit trail service"
                technology "AWS CloudTrail"
                tag "Amazon Web Services - CloudTrail"
            }
            
            # CI/CD Pipeline
            codeBuild = container "AWS CodeBuild" {
                description "Build service for compiling and testing code"
                technology "AWS CodeBuild"
                tag "Amazon Web Services - CodeBuild"
            }
            
            codePipeline = container "AWS CodePipeline" {
                description "Continuous delivery pipeline"
                technology "AWS CodePipeline"
                tag "Amazon Web Services - CodePipeline"
            }
            
            codeArtifact = container "AWS CodeArtifact" {
                description "Artifact repository for storing build outputs"
                technology "AWS CodeArtifact"
                tag "Amazon Web Services - CodeArtifact"
            }
        }
        
        # Relationships - User Interactions
        endUser -> awsInfrastructure.cloudFront "Accesses web applications"
        contentManager -> awsInfrastructure.transferFamily "Uploads large files via SFTP"
        developer -> awsInfrastructure.codePipeline "Triggers deployments"
        
        # Content Delivery Flow
        awsInfrastructure.cloudFront -> awsInfrastructure.s3 "Serves static content from"
        awsInfrastructure.cloudFront -> awsInfrastructure.alb "Routes API requests to"
        
        # Load Balancing & Backend
        awsInfrastructure.alb -> awsInfrastructure.beanstalk "Distributes traffic to"
        awsInfrastructure.alb -> awsInfrastructure.ec2 "Distributes traffic to"
        awsInfrastructure.beanstalk -> awsInfrastructure.aurora "Reads/writes data to"
        awsInfrastructure.ec2 -> awsInfrastructure.aurora "Reads/writes data to"
        awsInfrastructure.beanstalk -> awsInfrastructure.elasticache "Caches data in"
        awsInfrastructure.ec2 -> awsInfrastructure.elasticache "Caches data in"
        
        # File Upload Flow
        awsInfrastructure.transferFamily -> awsInfrastructure.s3 "Stores uploaded files in"
        
        # Analytics Pipeline
        awsInfrastructure.apiGateway -> awsInfrastructure.lambda "Triggers analytics processing"
        awsInfrastructure.lambda -> awsInfrastructure.sqs "Queues events in"
        awsInfrastructure.sqs -> awsInfrastructure.s3 "Stores processed analytics in"
        
        # CI/CD Pipeline
        awsInfrastructure.codeBuild -> awsInfrastructure.codePipeline "Builds artifacts for"
        awsInfrastructure.codePipeline -> awsInfrastructure.codeArtifact "Stores artifacts in"
        awsInfrastructure.codePipeline -> awsInfrastructure.s3 "Deploys web apps to"
        awsInfrastructure.codePipeline -> awsInfrastructure.beanstalk "Deploys backend to"
        
        # Security & Certificates
        awsInfrastructure.acm -> awsInfrastructure.cloudFront "Provides SSL certificates to"
        awsInfrastructure.acm -> awsInfrastructure.alb "Provides SSL certificates to"
        awsInfrastructure.iam -> awsInfrastructure.beanstalk "Manages permissions for"
        awsInfrastructure.iam -> awsInfrastructure.lambda "Manages permissions for"
        awsInfrastructure.iam -> awsInfrastructure.transferFamily "Manages permissions for"
        
        # Monitoring & Notifications
        awsInfrastructure.cloudWatch -> awsInfrastructure.sns "Sends alerts via"
        awsInfrastructure.cloudWatch -> awsInfrastructure.ses "Sends email notifications via"
        awsInfrastructure.cloudTrail -> awsInfrastructure.cloudWatch "Logs API calls to"
        awsInfrastructure.sns -> externalMonitoring "Notifies external systems"
        
        # Scheduled Jobs
        awsInfrastructure.eventBridge -> awsInfrastructure.lambda "Triggers scheduled jobs"
        awsInfrastructure.eventBridge -> awsInfrastructure.beanstalk "Triggers scheduled tasks"
        
        # Email Services
        awsInfrastructure.beanstalk -> awsInfrastructure.ses "Sends emails via"
        awsInfrastructure.lambda -> awsInfrastructure.ses "Sends emails via"
    }
    
    views {
        
        systemContext awsInfrastructure {
            include *
            autolayout lr 300 300
            title "AWS Infrastructure - System Context"
            description "High-level view of the AWS infrastructure and external actors"
        }

        container awsInfrastructure "FullInfrastructure" {
            include *
            autolayout tb 400 300
            title "AWS Infrastructure - Complete View"
            description "Complete view of all AWS services and their relationships"
        }

        container awsInfrastructure "ComputeAndStorage" {
            include awsInfrastructure.s3
            include awsInfrastructure.cloudFront
            include awsInfrastructure.beanstalk
            include awsInfrastructure.ec2
            include awsInfrastructure.aurora
            include awsInfrastructure.elasticache
            include awsInfrastructure.alb
            include endUser
            autolayout lr 400 300
            title "Core Compute & Storage Services"
            description "Main application infrastructure components"
        }

        container awsInfrastructure "Analytics" {
            include awsInfrastructure.apiGateway
            include awsInfrastructure.lambda
            include awsInfrastructure.sqs
            include awsInfrastructure.s3
            include endUser
            autolayout lr 350 250
            title "Analytics Pipeline"
            description "Data analytics and processing flow"
        }

        container awsInfrastructure "CICD" {
            include awsInfrastructure.codeBuild
            include awsInfrastructure.codePipeline
            include awsInfrastructure.codeArtifact
            include awsInfrastructure.s3
            include awsInfrastructure.beanstalk
            include developer
            autolayout lr 350 250
            title "CI/CD Pipeline"
            description "Continuous integration and deployment workflow"
        }

        container awsInfrastructure "Security" {
            include awsInfrastructure.iam
            include awsInfrastructure.acm
            include awsInfrastructure.cloudFront
            include awsInfrastructure.alb
            include awsInfrastructure.cloudTrail
            include awsInfrastructure.cloudWatch
            autolayout tb 350 250
            title "Security & Monitoring"
            description "Security, certificates, and monitoring services"
        }

        container awsInfrastructure "SimplifiedView" {
            include endUser
            include contentManager
            include developer
            include awsInfrastructure.cloudFront
            include awsInfrastructure.alb
            include awsInfrastructure.beanstalk
            include awsInfrastructure.aurora
            include awsInfrastructure.s3
            include awsInfrastructure.transferFamily
            include awsInfrastructure.codePipeline
            autolayout tb 500 400
            title "Simplified Infrastructure View"
            description "Key services and main data flows only"
        }
        
        styles {
            element "person" {
                shape person
                fontSize 14
                background #08427b
                color #ffffff
            }

            element "external" {
                background #999999
                color #ffffff
                shape RoundedBox
            }

            element "Software System" {
                background transparent
                color #000000
                stroke transparent
                shape RoundedBox
                fontSize 16
            }

            element "Container" {
                background transparent
                color #000000
                stroke transparent
                shape RoundedBox
                fontSize 11
                width 150
                height 150
            }

            relationship "Relationship" {
                color #666666
                thickness 1
                style solid
                routing Orthogonal
                fontSize 0
                opacity 70
            }

            # Color-coded relationships without labels
            relationship "Accesses web applications" {
                color #1976d2
                thickness 2
                fontSize 0
            }

            relationship "Uploads large files via SFTP" {
                color #ff9800
                thickness 1
                fontSize 0
            }

            relationship "Triggers deployments" {
                color #9c27b0
                thickness 1
                fontSize 0
            }

            relationship "Serves static content from" {
                color #4caf50
                thickness 1
                fontSize 0
            }

            relationship "Distributes traffic to" {
                color #f44336
                thickness 2
                fontSize 0
            }

            relationship "Reads/writes data to" {
                color #795548
                thickness 1
                fontSize 0
            }

            relationship "Caches data in" {
                color #607d8b
                thickness 1
                fontSize 0
            }

            relationship "Stores uploaded files in" {
                color #ff5722
                thickness 1
                fontSize 0
            }

            relationship "Triggers analytics processing" {
                color #3f51b5
                thickness 1
                fontSize 0
            }

            relationship "Provides SSL certificates to" {
                color #e91e63
                thickness 1
                fontSize 0
            }
        }
        
        # Apply AWS theme for proper icons
        theme https://static.structurizr.com/themes/amazon-web-services-2023.01.31/theme.json
    }
}
